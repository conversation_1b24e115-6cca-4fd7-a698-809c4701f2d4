import sys
from logging import getLogger

import sub.jba_live as jba_live_sub
import sub.jba_tokyo as jba_tokyo
import sub.stay_gold as stay_gold_sub
import sub.timeless as timeless_sub

# import timeless
from util import config_util, log_util
from version import __version__

# 設定ファイル
CONFIG_FILE = "config/config.json"
# ロガー設定ファイル
LOGGING_CONFIG = "config/logging.conf"

# ロガー初期設定
log_util.init_logger(LOGGING_CONFIG)

# ロガー取得
logger = getLogger()


def main():
    try:
        logger.info(f"処理開始　バージョン：{__version__}")

        # 設定ファイルの読み込み
        config = config_util.get_config(CONFIG_FILE)
        logger.info(f"設定情報：{config}")

        # コマンドライン引数から動作モードを取得
        mode = ""

        # DEBUG
        if len(sys.argv) == 1:
            sys.argv.append("jba_live")

        if len(sys.argv) > 1:
            mode = sys.argv[1]
            logger.info(f"動作モード：{mode}")
        else:
            # 動作モードが指定されていない場合はエラー
            raise Exception("動作モードが指定されていません")

        match mode:
            case "jba_live":
                # JBAライブの情報を取得
                jba_live_sub.exec(config)
            case "jba_tokyo":
                # JBA東京の情報を取得
                jba_tokyo.exec(config)
                pass
            case "stay_gold":
                # STAY GOLDの情報を取得
                stay_gold_sub.exec(config)
                pass
            case "timeless":
                # TIMELESSの情報を取得
                timeless_sub.exec(config)
                pass
            case _:
                raise Exception("動作モードが不正です")

        logger.info("正常終了しました")
    except Exception:
        logger.exception("エラー発生、処理を中止します")
        sys.exit(1)


if __name__ == "__main__":
    main()
