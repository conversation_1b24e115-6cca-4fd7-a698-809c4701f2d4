[loggers]
keys=root

[handlers]
keys=file<PERSON><PERSON><PERSON>,console<PERSON><PERSON><PERSON>

[formatters]
keys=fileFormatter,consoleFormatter

[logger_root]
level=INFO
handlers=fileHandler,consoleHandler

[handler_fileHandler]
class=logging.handlers.TimedRotatingFileHandler
level=INFO
formatter=fileFormatter
args=("./log/auction_scraper.log", "midnight", 1, 7)

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=consoleFormatter
args=(sys.stdout,)

[formatter_fileFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(filename)-25s:%(lineno)-4d - %(message)s

[formatter_consoleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s
