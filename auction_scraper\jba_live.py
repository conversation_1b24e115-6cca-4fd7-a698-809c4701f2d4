from logging import getLogger

import jba_common
from util import selenium_util

logger = getLogger()


def exec(config: dict):
    """
    JBAライブの情報を取得
    :param config: 設定情報
    """
    logger.info("jba_live:開始")

    # seleniumのドライバー
    driver = None

    try:
        # ブラウザを起動
        driver = jba_common.start_browser(config)

        # ログイン
        jba_common.login(driver, config)

        # JBAライブ　自社売買一覧（清算書）クリック
        selenium_util.click_element_by_css_selector(
            driver, "li.padding_2.li_kaijo3.menu.menu_torihikiJokyoIchiran"
        )

        # すべてのデータを取得
        jba_common.get_all_data_and_image(
            driver,
            config["jba_live"]["download_dir"],
            config["jba_live"]["png_dir"],
            config["jba_live"]["jpg_dir"],
            config["jba_live"]["resized_jpg_dir"],
            config["jba_live"]["prefix"],
        )

    finally:
        # ブラウザを終了
        if driver:
            driver.quit()
