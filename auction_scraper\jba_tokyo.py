import sys
from logging import getLogger

import sub.jba_tokyo as jba_tokyo_sub
from util import config_util, log_util
from version import __version__

# 設定ファイル
CONFIG_FILE = "config/config.json"
# ロガー設定ファイル
LOGGING_CONFIG = "config/logging.conf"

# ロガー初期設定
log_util.init_logger(LOGGING_CONFIG)

# ロガー取得
logger = getLogger()


def main():
    try:
        logger.info(f"処理開始　バージョン：{__version__}")

        # 設定ファイルの読み込み
        config = config_util.get_config(CONFIG_FILE)
        logger.info(f"設定情報：{config}")

        # JBA東京の情報を取得
        jba_tokyo_sub.exec(config)

        logger.info("正常終了しました")
    except Exception:
        logger.exception("エラー発生、処理を中止します")
        sys.exit(1)


if __name__ == "__main__":
    main()
