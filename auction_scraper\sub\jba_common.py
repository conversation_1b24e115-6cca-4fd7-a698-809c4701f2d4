import os
import re
import time
from logging import getLogger

from PIL import Image

from util import excel_util, img_util, selenium_util

logger = getLogger()


def start_browser(config: dict):
    """
    ブラウザを起動
    :param config: 設定情報
    """
    # ブラウザを起動
    driver = selenium_util.get_driver(config["selenium"]["options"])

    # タイムアウト設定を調整
    driver.set_page_load_timeout(120)  # ページロードタイムアウトを120秒に
    driver.set_script_timeout(60)  # スクリプトタイムアウトを60秒に

    # URLを開く
    selenium_util.open_url(
        driver, config["jba_common"]["url"], config["selenium"]["max_wait_time"]
    )

    return driver


def login(driver, config):
    """
    ログイン
    :param driver: ドライバー
    :param config: 設定情報
    """
    logger.info("ログインID入力")
    selenium_util.input_text_by_css_selector(
        driver, "input#login_id", config["jba_common"]["login_id"]
    )

    logger.info("パスワード入力")
    selenium_util.input_text_by_css_selector(
        driver, "input#password", config["jba_common"]["password"]
    )

    logger.info("ログインボタンクリック")
    selenium_util.click_element_by_css_selector(driver, "button#login_button")


def display_auction_items_list(driver):
    """
    落札商品一覧表示
    :param driver: ドライバー
    """
    logger.info("先頭の売買一覧をクリック")
    selenium_util.click_element_by_css_selector(
        driver, "button.baibai_ichiran_button_all"
    )

    logger.info("買い明細をクリック")
    selenium_util.click_element_by_css_selector(
        driver, "label#shuppin_nyusatsu_label_2.shuppin_nyusatsu_label"
    )

    logger.info("表示件数を1000件に変更")
    selenium_util.select_option_by_css_selector(
        driver, "select#kensu_pulldown1_pc", "1000"
    )

    logger.info("表示ボタンをクリック")
    selenium_util.click_element_by_css_selector(driver, "button.torihiki_hyoji_button")


def get_item_details(driver):
    """
    商品詳細データ取得
    :param driver: ドライバー
    :param config: 設定情報
    """
    # 取得データを格納するJSON
    data = {}

    # 開催日取得
    kaisaibi = selenium_util.get_text_by_css_selector(
        driver, "div.kaisaibi_lane div span.kaisaibi"
    )
    # スラッシュをハイフンに変換
    data["開催日"] = kaisaibi.replace("/", "-") if kaisaibi else None

    # ロット取得
    data["ロット"] = selenium_util.get_text_by_css_selector(
        driver, "div.kaisaibi_lot span.lot"
    )

    # ランク取得
    data["ランク"] = selenium_util.get_text_by_css_selector(
        driver, "td.rank_col span.rank"
    )

    # 備考取得
    data["備考"] = selenium_util.get_text_by_css_selector(
        driver, "div.shohin_shosai table.table tbody tr td.biko"
    )

    # 詳細取得
    data["詳細"] = selenium_util.get_text_by_css_selector(
        driver, "div.shohin_shosai table.table tbody tr td.shosai"
    )

    # ID取得
    data["ID"] = selenium_util.get_text_by_css_selector(driver, "span#disp_auction_id")

    logger.info(f"取得データ：{data}")

    return data


def make_download_dir(download_dir_name, kaisaibi):
    """
    ダウンロードディレクトリ作成
    :param dir_name: ダウンロードディレクトリ名
    :param kaisaibi: 開催日
    """
    # downloadディレクトリ
    download_dir = os.path.join(download_dir_name, kaisaibi)

    # ダウンロードディレクトリが存在しない場合は作成
    os.makedirs(download_dir, exist_ok=True)

    return download_dir


def make_sub_dir(download_dir, sub_dir_name):
    """
    サブディレクトリ作成
    :param download_dir: ダウンロードディレクトリ
    :param sub_dir_name: サブディレクトリ名
    """
    # サブディレクトリ
    sub_dir = os.path.join(download_dir, sub_dir_name)

    # サブディレクトリが存在しない場合は作成
    os.makedirs(sub_dir, exist_ok=True)

    return sub_dir


def get_kaisaibi(driver):
    """
    開催日取得
    :param driver: ドライバー
    """
    # 開催日
    kaisaibi = None

    # 最大試行回数
    max_retries = 5

    # 開催日取得
    for _ in range(max_retries):
        kaisaibi = selenium_util.get_text_by_css_selector(
            driver, "td#urikaiGoukei_kaisaibi"
        )
        if kaisaibi and is_valid_date(kaisaibi):
            break
        logger.info(
            f"開催日が取得できませんでした。再試行します...　開催日: {kaisaibi or 'None'}"
        )
        # 3秒待機
        time.sleep(3)

    # 正常な開催日が取得できない場合はエラー
    if not kaisaibi or not is_valid_date(kaisaibi):
        raise Exception("開催日が不正です", kaisaibi)

    # スラッシュをハイフンに変換
    kaisaibi = kaisaibi.replace("/", "-")

    return kaisaibi


def is_valid_date(date_str):
    """
    文字列がyyyy/mm/dd形式の日付であるか判定
    月や日は一桁の場合もあることに対応
    :param date_str: 日付文字列
    :return: 有効な日付の場合はTrue、そうでない場合はFalse
    """
    pattern = r"^\d{4}/\d{1,2}/\d{1,2}$"
    if re.match(pattern, date_str):
        return True
    return False


def get_all_data_and_image(
    driver,
    download_dir_name,
    png_dir_name,
    jpg_dir_name,
    resized_jpg_dir_name,
    excel_prefix,
):
    """
    すべてのデータと画像を取得
    :param driver: ドライバー
    :param download_dir_name: ダウンロードディレクトリ名
    :param png_dir_name: PNGディレクトリ名
    :param jpg_dir_name: JPGディレクトリ名
    :param resized_jpg_dir_name: リサイズJPGディレクトリ名
    :param excel_prefix: Excelファイルプ
    """
    # 落札商品一覧表示
    display_auction_items_list(driver)

    # 開催日取得
    kaisaibi = get_kaisaibi(driver)
    logger.info(f"開催日：{kaisaibi}")

    # ダウンロードディレクトリ作成
    download_dir = make_download_dir(download_dir_name, kaisaibi)
    logger.info(f"ダウンロードディレクトリ：{download_dir}")
    # PNGディレクトリ作成
    png_dir = make_sub_dir(download_dir, png_dir_name)
    logger.info(f"PNGディレクトリ：{png_dir}")
    # JPGディレクトリ作成
    jpg_dir = make_sub_dir(download_dir, jpg_dir_name)
    logger.info(f"JPGディレクトリ：{jpg_dir}")
    # リサイズJPGディレクトリ作成
    resized_jpg_dir = make_sub_dir(download_dir, resized_jpg_dir_name)
    logger.info(f"リサイズJPGディレクトリ：{resized_jpg_dir}")

    # 最初の詳細ボタンをクリック
    selenium_util.click_element_by_css_selector(driver, "button.shosai_button")
    # ローディング画面が消えるまで待機
    selenium_util.wait_until_loader_disappears(driver)

    data_list = []

    while True:
        # 自社落札の要素が存在するか
        is_rakusatsu = selenium_util.is_element_present_by_css_selector(
            driver, "div.seri_kekka.jisha_rakusatsu"
        )
        logger.info(f"自社落札：{is_rakusatsu}")
        # 自社落札の場合のみ処理
        if is_rakusatsu:
            # 商品詳細データ取得
            data = get_item_details(driver)
            data_list.append(data)

            # ファイルプレフィックス
            file_prefix = data["ID"] + "_"

            # サムネイル要素を取得
            try:
                # 明示的に待機を入れる
                import time

                time.sleep(1)

                thumb_elements = selenium_util.get_all_elements_by_css_selector(
                    driver, "img.thumb_image"
                )

                # サムネイルが見つからない場合のログ
                if not thumb_elements:
                    logger.warning("サムネイル要素が見つかりませんでした")

                # サムネイルを順番にクリック
                for i, thumb_element in enumerate(thumb_elements, start=1):
                    try:
                        # サムネイルをクリック
                        if i > 1:
                            # 明示的に待機
                            time.sleep(0.5)
                            selenium_util.click_element(driver, thumb_element)
                            # ローディング画面が消えるまで待機
                            selenium_util.wait_until_loader_disappears(driver)
                            # クリック後も少し待機
                            time.sleep(0.5)

                        # pngファイル名
                        png_file = os.path.join(png_dir, file_prefix + f"{i:02}.png")
                        # canvas要素からイメージファイルをダウンロード
                        selenium_util.download_canvas_image_by_css_selector(
                            driver, "div#main_image canvas", png_file
                        )

                        # pngファイルが存在する場合のみ処理
                        if not os.path.exists(png_file):
                            logger.warning(
                                f"PNGファイルが作成されませんでした: {png_file}"
                            )
                            continue

                        logger.info(f"PNGファイル：{png_file}")

                        # jpgファイル名
                        jpg_file = os.path.join(jpg_dir, file_prefix + f"{i:02}.jpg")
                        logger.info(f"JPGファイル：{jpg_file}")
                        # pngファイルをjpgファイルに変換
                        img_util.convert_png_to_jpg(png_file, jpg_file)

                        # resized jpgファイル名
                        resized_jpg_file = os.path.join(
                            resized_jpg_dir, file_prefix + f"{i:02}.jpg"
                        )
                        logger.info(f"Resized JPGファイル：{resized_jpg_file}")
                        # jpgファイルをリサイズ
                        img_width = 0
                        with Image.open(jpg_file) as img:
                            img_width = img.width
                        img_util.resize_jpg(jpg_file, resized_jpg_file, 347 / img_width)
                    except Exception as thumb_error:
                        logger.error(f"サムネイル処理エラー ({i}番目): {thumb_error}")
                        # エラーがあっても次のサムネイルへ
                        continue
            except Exception as e:
                logger.error(f"画像処理全体エラー: {e}")
                # 画像処理でエラーが発生しても次の商品へ

        # 次ボタンが無効の場合は終了
        if selenium_util.is_disabled_element_by_css_selector(
            driver, "button.tsugi_button"
        ):
            # Excel出力
            excel_file = os.path.join(
                download_dir,
                excel_prefix + kaisaibi + ".xlsx",
            )
            excel_util.json_to_excel(data_list, excel_file)
            logger.info(f"Excelファイル：{excel_file}")
            break

        # 次ボタンをクリック
        selenium_util.click_element_by_css_selector(driver, "button.tsugi_button")
        # ローディング画面が消えるまで待機
        selenium_util.wait_until_loader_disappears(driver)
