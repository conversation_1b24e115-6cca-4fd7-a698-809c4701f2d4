from logging import getLogger

import sub.jba_common as jba_common
from util import selenium_util

logger = getLogger()


def exec(config: dict):
    """
    JBA東京の情報を取得
    :param config: 設定情報
    """
    logger.info("jba_tokyo:開始")

    # seleniumのドライバー
    driver = None

    try:
        # ブラウザを起動
        driver = jba_common.start_browser(config)

        # ログイン
        jba_common.login(driver, config)

        # JBA東京　自社売買一覧（清算書）クリック
        selenium_util.click_element_by_css_selector(
            driver, "li.padding_2.li_kaijo2.menu.menu_torihikiJokyoIchiran"
        )

        # すべてのデータを取得
        jba_common.get_all_data_and_image(
            driver,
            config["jba_tokyo"]["download_dir"],
            config["jba_tokyo"]["png_dir"],
            config["jba_tokyo"]["jpg_dir"],
            config["jba_tokyo"]["resized_jpg_dir"],
            config["jba_tokyo"]["prefix"],
        )

    finally:
        # ブラウザを終了
        if driver:
            driver.quit()
