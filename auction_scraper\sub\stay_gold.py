import os
import re
from datetime import datetime, timedelta
from logging import getLogger

from PIL import Image
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select

from util import excel_util, img_util, selenium_util

logger = getLogger()


def exec(config: dict):
    """
    StayGoldの情報を取得
    :param config: 設定情報
    """
    logger.info("stay_gold:開始")

    # seleniumのドライバー
    driver = None

    try:
        # ブラウザを起動
        driver = start_browser(config)

        # ログイン
        login(driver, config)

        # 精算画面へ遷移
        move_to_seisan(driver)

        # 開催回を選択し、開催日を取得
        kaisaibi = select_kaisaibi(driver)
        logger.info(f"開催日：{kaisaibi}")

        # 落札商品一覧表示
        display_auction_items_list(driver)

        # ダウンロードディレクトリ作成
        download_dir = make_download_dir(config["stay_gold"]["download_dir"], kaisaibi)
        # jpgディレクトリ作成
        jpg_dir = make_sub_dir(download_dir, config["stay_gold"]["jpg_dir"])
        # resizedディレクトリ作成
        resized_jpg_dir = make_sub_dir(
            download_dir, config["stay_gold"]["resized_jpg_dir"]
        )

        # すべての商品IDを取得
        id_list = get_all_goods_id(driver)
        logger.info(f"商品IDリスト：{id_list}")

        # 取得データリスト
        data_list = []

        # 商品詳細画面へ遷移してデータ取得
        for id in id_list:
            # 商品詳細画面へ遷移
            selenium_util.open_url(driver, config["stay_gold"]["detail_url"] + id)

            # データ取得
            data = getData(driver)

            # データリストに追加
            data_list.append(data)

            # 画像ファイルのダウンロード
            download_images(driver, jpg_dir, resized_jpg_dir, kaisaibi, data["LOT"])

        # Excel出力
        excel_file = os.path.join(
            download_dir,
            config["stay_gold"]["prefix"] + kaisaibi + ".xlsx",
        )
        excel_util.json_to_excel(data_list, excel_file)
        logger.info(f"Excelファイル：{excel_file}")

    finally:
        # ブラウザを終了
        if driver:
            driver.quit()


def start_browser(config: dict):
    """
    ブラウザを起動
    :param config: 設定情報
    """
    # ブラウザを起動
    driver = selenium_util.get_driver(config["selenium"]["options"])
    selenium_util.open_url(
        driver, config["stay_gold"]["url"], config["selenium"]["max_wait_time"]
    )

    return driver


def login(driver, config):
    """
    ログイン
    :param driver: ドライバー
    :param config: 設定情報
    """
    # jqueryの処理待ち
    selenium_util.wait_until_jquery_is_done(driver)

    # ログインID入力
    selenium_util.input_text_by_css_selector(
        driver, "input#loginId", config["stay_gold"]["login_id"]
    )
    # パスワード入力
    selenium_util.input_text_by_css_selector(
        driver, "input#password", config["stay_gold"]["password"]
    )
    # ログインボタンクリック
    selenium_util.click_element_by_css_selector(
        driver, "button#loginBtn.btn.btn-action.btn-block"
    )


def move_to_seisan(driver):
    """
    精算画面へ遷移
    :param driver: ドライバー
    """
    # jqueryの処理待ち
    selenium_util.wait_until_jquery_is_done(driver)

    # 精算クリック
    selenium_util.click_element_by_xpath(
        driver, "//a[contains(@class, 'dropdown-toggle') and .//span[text()='精算']]"
    )

    # 落札精算クリック
    selenium_util.click_element_by_xpath(
        driver, "//ul[contains(@class, 'dropdown-menu')]/li/a[text()='落札精算']"
    )


def display_auction_items_list(driver):
    """
    落札商品一覧表示
    :param driver: ドライバー
    """
    # jqueryの処理待ち
    selenium_util.wait_until_jquery_is_done(driver)

    # 表示件数を100件に変更
    selenium_util.select_option_by_css_selector(
        driver, "select#viewNum.form-control", "100"
    )

    # 検索ボタンをクリック
    selenium_util.click_element_by_css_selector(driver, "button#searchBtn.btn-action")


def get_all_goods_id(driver):
    """
    すべての商品IDを取得
    :param driver: ドライバー
    :param config: 設定情報
    :return: 開催日
    """
    # jqueryの処理待ち
    selenium_util.wait_until_jquery_is_done(driver)

    # すべての商品IDを取得
    all_goods_id = selenium_util.get_all_elements_by_css_selector(
        driver, "tbody#list_body tr input.GoodsId"
    )
    logger.info(f"商品数：{len(all_goods_id)}")

    # 商品IDのリスト
    id_list = []

    for goods_id in all_goods_id:
        # idを取得
        id = selenium_util.get_text(goods_id)
        id_list.append(id)

    return id_list


def select_kaisaibi(driver):
    """
    開催回を選択し、選択した開催日を返す
    :param driver: ドライバー
    :return: 選択した開催日
    """
    # jqueryの処理待ち
    selenium_util.wait_until_jquery_is_done(driver)

    # 実際にはURLが変更されているので、URLを変更
    logger.info(f"変更前current_url: {driver.current_url}")
    driver.get("https://system.reva-auc.com/mem_resultlist")
    logger.info(f"変更後current_url: {driver.current_url}")

    # 開催回のセレクトを取得
    select_element = selenium_util.get_element_by_css_selector(
        driver, "select#auctionSeq.form-control"
    )

    # `<option>` を取得
    options = select_element.find_elements(By.TAG_NAME, "option")

    # 現在の日付の前日
    yesterday = datetime.today() - timedelta(days=1)
    logger.info(f"前日：{yesterday}")

    # 日付の正規表現パターン
    date_pattern = re.compile(r"(\d{4})年(\d{1,2})月(\d{1,2})日")

    # 条件に合う `option` のリスト
    valid_options = []

    for option in options:
        text = option.text.strip()
        value = option.get_attribute("value")

        # 「バッグ」で始まるかチェック
        if text.startswith("バッグ"):
            # 日付部分を抽出
            match = date_pattern.search(text)
            if match:
                year, month, day = map(int, match.groups())
                option_date = datetime(year, month, day)

                # 前日以前の日付をフィルタリング
                if option_date <= yesterday:
                    valid_options.append((option_date, value, text))

    logger.info(f"該当するオプション：{valid_options}")

    # 一番新しい日付を持つ `option` を取得
    if valid_options:
        valid_options.sort(reverse=True)  # 日付でソート
        newest_option = valid_options[0]  # 最も新しい日付の要素を取得
        selected_value = newest_option[1]
        logger.info(f"開催回: {selected_value}: {newest_option[2]}")

        # Seleniumで選択
        select = Select(select_element)
        select.select_by_value(selected_value)
    else:
        raise Exception("開催回の選択に失敗しました")

    # 開催日を取得
    kaisaibi = newest_option[2]

    # yyyymmddに編集
    kaisaibi = convert_kaisaibi(kaisaibi)

    return kaisaibi


def convert_kaisaibi(kaisaibi):
    """
    開催日をyyyymmddに編集
    :param kaisaibi: 開催回
    :return: yyyymmdd
    """
    # 日付の正規表現パターン
    date_pattern = re.compile(r"(\d{4})年(\d{1,2})月(\d{1,2})日")

    # 日付部分を抽出
    match = date_pattern.search(kaisaibi)
    if match:
        year, month, day = map(int, match.groups())
        kaisaibi = f"{year:04}{month:02}{day:02}"

    return kaisaibi


def getData(driver):
    """
    データ取得
    :param driver: ドライバー
    :param config: 設定情報
    """
    # jqueryの処理待ち
    selenium_util.wait_until_jquery_is_done(driver)

    # 取得データを格納するJSON
    data = {}

    # 商品名
    data["商品名"] = selenium_util.get_text_by_css_selector(driver, "div.goodsName")

    # 「discription-form」クラスをもつ親要素を取得
    discription_form = driver.find_element(By.CSS_SELECTOR, "div.discription-form")

    # その中にある「row discripation-row」を全て取得
    rows = discription_form.find_elements(By.CSS_SELECTOR, "div.row.discripation-row")

    for row in rows:
        # タイトル要素と値要素を取得
        title_elem = row.find_element(By.CSS_SELECTOR, "span.goods_data_title")
        value_elem = row.find_element(By.CSS_SELECTOR, "span.goods_data_value")

        # テキストを取り出して辞書に格納（strip()で前後の空白を除去）
        title_text = title_elem.text.strip()
        value_text = value_elem.text.strip()

        data[title_text] = value_text

    # 開催日
    data["開催日"] = convert_kaisaibi(data["開催回"])

    logger.info(f"取得データ：{data}")

    return data


def download_images(driver, jpg_dir, resized_jpg_dir, kaisaibi, lot):
    """
    画像ファイルのダウンロード
    :param driver: ドライバー
    :param jpg_dir: jpgディレクトリ
    :param resized_jpg_dir: resizedディレクトリ
    :param kaisaibi: 開催日
    :param lot: ロット
    """
    # 画像枚数取得
    image_count = get_image_count(driver)

    # 画像ダウンロード
    for i in range(1, image_count + 1):
        # 2～5枚目と15枚目以降の画像は不要
        if (2 <= i <= 5) or (15 <= i):
            continue

        # 画像URL
        img_url = ""
        # data-slick-index=i を持つ要素を取得
        element = selenium_util.get_element_by_css_selector(
            driver, f"[data-slick-index='{i}']"
        )
        # imgのonclickを取得
        onclick_value = element.find_element(By.CSS_SELECTOR, "img").get_attribute(
            "onclick"
        )
        # 正規表現を使ってURL部分を抽出
        match = re.search(r"thumbImgClick\('([^']+)'", onclick_value)
        if match:
            img_url = match.group(1)

        # JPG画像ファイル名
        jpg_file = os.path.join(jpg_dir, f"{kaisaibi}_{lot}_{i:02}.jpg")
        logger.info(f"JPGファイル：{img_url} -> {jpg_file}")

        # 画像ダウンロード
        selenium_util.download_image(driver, img_url, jpg_file)

        # リサイズJPG画像ファイル名
        resized_jpg_file = os.path.join(resized_jpg_dir, f"{kaisaibi}_{lot}_{i:02}.jpg")
        logger.info(f"Resized JPGファイル：{resized_jpg_file}")

        # 画像リサイズ
        img_width = 0
        with Image.open(jpg_file) as img:
            img_width = img.width
        img_util.resize_jpg(jpg_file, resized_jpg_file, 347 / img_width)


def get_image_count(driver):
    """
    画像枚数取得
    :param driver: ドライバー
    :return: 画像枚数
    """
    # data-slick-index="-1" を持つ要素を取得
    element = selenium_util.get_element_by_css_selector(
        driver, "[data-slick-index='-1']"
    )

    input_element = element.find_element(By.CSS_SELECTOR, "input.imgHidNo")
    image_count = int(input_element.get_attribute("value"))

    return image_count


def make_download_dir(download_dir_name, kaisaibi):
    """
    ダウンロードディレクトリ作成
    :param download_dir_name: ダウンロードディレクトリ名
    :param kaisaibi: 開催日
    :return: ダウンロードディレクトリ
    """
    # ダウンロードディレクトリ
    download_dir = os.path.join(download_dir_name, kaisaibi)

    # ダウンロードディレクトリ作成
    os.makedirs(download_dir, exist_ok=True)

    return download_dir


def make_sub_dir(download_dir, sub_dir_name):
    """
    サブディレクトリ作成
    :param download_dir: ダウンロードディレクトリ
    :param sub_dir_name: サブディレクトリ名
    :return: サブディレクトリ
    """
    # サブディレクトリ
    sub_dir = os.path.join(download_dir, sub_dir_name)

    # サブディレクトリ作成
    os.makedirs(sub_dir, exist_ok=True)

    return sub_dir
