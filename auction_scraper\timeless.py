import os
import re
from datetime import datetime
from logging import getLogger

from PIL import Image
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select

from util import excel_util, img_util, selenium_util

logger = getLogger()


def exec(config: dict):
    """
    TIMELESSの情報を取得
    :param config: 設定情報
    """
    logger.info("timeless:開始")

    # seleniumのドライバー
    driver = None

    try:
        # ブラウザを起動
        driver = start_browser(config)

        # ログイン
        login(driver, config)

        # 入札管理クリック
        selenium_util.click_element_by_xpath(driver, "//button[contains(.,'入札管理')]")

        # 開催回を選択し、開催日を取得
        kaisaibi = select_kaisaibi(driver)
        logger.info(f"開催日：{kaisaibi}")

        # 落札商品一覧表示
        display_auction_items_list(driver)

        # ダウンロードディレクトリ作成
        download_dir = make_download_dir(config["timeless"]["download_dir"], kaisaibi)
        # jpgディレクトリ作成
        jpg_dir = make_sub_dir(download_dir, config["timeless"]["jpg_dir"])
        # resizedディレクトリ作成
        resized_jpg_dir = make_sub_dir(
            download_dir, config["timeless"]["resized_jpg_dir"]
        )

        # すべての商品のリンク先URLを取得
        url_list = get_all_goods_url(driver)
        logger.info(f"商品URL件数：{len(url_list)}")
        logger.info(f"商品URLリスト：{url_list}")

        # 取得データリスト
        data_list = []

        # 商品詳細画面へ遷移してデータ取得
        for url in url_list:
            # 商品詳細画面へ遷移
            selenium_util.open_url(driver, url)

            # 商品ID取得
            goods_id = url.split("/")[-1]

            # データ取得
            data = getData(driver, goods_id)

            # データリストに追加
            data_list.append(data)

            # 画像ファイルのダウンロード
            download_images(driver, jpg_dir, resized_jpg_dir, kaisaibi, goods_id)

        # Excel出力
        excel_file = os.path.join(
            download_dir,
            config["timeless"]["prefix"] + kaisaibi + ".xlsx",
        )
        excel_util.json_to_excel(data_list, excel_file)
        logger.info(f"Excelファイル：{excel_file}")

    finally:
        # ブラウザを終了
        if driver:
            driver.quit()


def start_browser(config: dict):
    """
    ブラウザを起動
    :param config: 設定情報
    """
    # ブラウザを起動
    driver = selenium_util.get_driver(config["selenium"]["options"])
    selenium_util.open_url(
        driver, config["timeless"]["url"], config["selenium"]["max_wait_time"]
    )

    return driver


def login(driver, config):
    """
    ログイン
    :param driver: ドライバー
    :param config: 設定情報
    """
    # ログインID入力
    selenium_util.input_text_by_css_selector(
        driver, "input[placeholder='メールアドレス']", config["timeless"]["login_id"]
    )
    # パスワード入力
    selenium_util.input_text_by_css_selector(
        driver, "input[placeholder='パスワード']", config["timeless"]["password"]
    )
    # ログインボタンクリック
    selenium_util.click_element_by_xpath(driver, "//button[contains(.,'ログイン')]")


def display_auction_items_list(driver):
    """
    落札商品一覧表示
    :param driver: ドライバー
    """
    # 絞り込むボタンをクリック
    selenium_util.click_element_by_xpath(driver, "//button[contains(.,'絞り込む')]")

    # 表示件数を100件に変更
    selenium_util.select_option_by_xpath(
        driver, '//p[text()="件表示"]/preceding-sibling::div//select', "100"
    )


def get_all_goods_url(driver):
    """
    すべての商品のURLを取得
    :param driver: ドライバー
    """
    # 要素が表示されるまで待機
    selenium_util.wait_until_element_present_by_css_selector(
        driver, "a[href*='/exhibit_products/']"
    )
    # すべての商品画面のhrefを含むaタグを取得
    elements = selenium_util.get_all_elements_by_css_selector(
        driver, "a[href*='/exhibit_products/']"
    )

    # 商品URLのリスト
    url_list = []
    for element in elements:
        # hrefを取得
        url = element.get_attribute("href")
        if url not in url_list:
            url_list.append(url)

    return url_list


def select_kaisaibi(driver):
    """
    開催回を選択し、選択した開催日を返す
    :param driver: ドライバー
    :return: 選択した開催日
    """
    # 開催回のセレクトを取得
    select_element = selenium_util.get_element_by_xpath(
        driver,
        '//div[@class="chakra-stack css-gsc7pt"][.//p[text()="オークション"]]//select',
    )

    # `<option>` を取得
    options = select_element.find_elements(By.TAG_NAME, "option")

    # 現在の日付の前日
    # yesterday = datetime.today() - timedelta(days=10)
    yesterday = datetime(2024, 7, 25)
    logger.info(f"前日：{yesterday}")

    # 日付の正規表現パターン
    date_pattern = re.compile(r"(\d{4})年(\d{1,2})月(\d{1,2})日")

    # 条件に合う `option` のリスト
    valid_options = []

    for option in options:
        text = option.text.strip()
        value = option.get_attribute("value")

        # 日付部分を抽出
        match = date_pattern.search(text)
        if match:
            year, month, day = map(int, match.groups())
            option_date = datetime(year, month, day)

            # 前日以前の日付をフィルタリング
            if option_date <= yesterday:
                valid_options.append((option_date, value, text))

    logger.info(f"該当するオプション：{valid_options}")

    # 一番新しい日付を持つ `option` を取得
    if valid_options:
        valid_options.sort(reverse=True)  # 日付でソート
        newest_option = valid_options[0]  # 最も新しい日付の要素を取得
        selected_value = newest_option[1]
        logger.info(f"開催回: {selected_value}: {newest_option[2]}")

        # Seleniumで選択
        select = Select(select_element)
        select.select_by_value(selected_value)
    else:
        raise Exception("開催回の選択に失敗しました")

    # 開催日を取得
    kaisaibi = newest_option[2]

    # yyyymmddに編集
    kaisaibi = convert_kaisaibi(kaisaibi)

    return kaisaibi


def convert_kaisaibi(kaisaibi):
    """
    開催日をyyyymmddに編集
    :param kaisaibi: 開催回
    :return: yyyymmdd
    """
    # 日付の正規表現パターン
    date_pattern = re.compile(r"(\d{4})年(\d{1,2})月(\d{1,2})日")

    # 日付部分を抽出
    match = date_pattern.search(kaisaibi)
    if match:
        year, month, day = map(int, match.groups())
        kaisaibi = f"{year:04}{month:02}{day:02}"

    return kaisaibi


def getData(driver, goods_id):
    """
    データ取得
    :param driver: ドライバー
    :param goods_id: 商品ID
    :return 取得データ
    """
    # ブランド名の要素が表示されるまで待機
    selenium_util.wait_until_element_present_by_xpath(
        driver,
        "//*[@id='__next']/div/div[2]/div[2]/div/div/div[2]/div[1]/div[2]/div[1]/div[1]/div/p[1]",
    )

    # 取得データを格納するJSON
    data = {}

    # ID
    data["ID"] = goods_id

    # ブランド名
    data["ブランド名"] = selenium_util.get_text_by_xpath(
        driver,
        "//*[@id='__next']/div/div[2]/div[2]/div/div/div[2]/div[1]/div[2]/div[1]/div[1]/div/p[1]",
    )

    # 商品名
    data["商品名"] = selenium_util.get_text_by_xpath(
        driver,
        "//*[@id='__next']/div/div[2]/div[2]/div/div/div[2]/div[1]/div[2]/div[1]/div[1]/div/p[2]",
    )

    # 商品詳細
    dtails = selenium_util.get_all_elements_by_xpath(
        driver, '//*[@id="__next"]/div/div[2]/div[2]/div/div/div[2]/dl/dd/ul/li'
    )

    # 商品詳細を取得
    for li in dtails:
        # タイトル部分 (dt 下の p)
        title_element = li.find_element(By.XPATH, ".//dl/dt/p")
        title_text = title_element.text

        # 値部分 (dt の後ろに出てくる p)
        value_element = li.find_element(By.XPATH, ".//dl/p[not(ancestor::dt)]")
        value_text = value_element.text
        data[title_text] = value_text

    # 備考
    data["備考"] = selenium_util.get_text_by_xpath(
        driver,
        '//*[@id="__next"]/div/div[2]/div[2]/div/div/div[2]/div[2]/div[1]/dl/dd/p',
    )

    logger.info(f"取得データ：{data}")

    return data


def download_images(driver, jpg_dir, resized_jpg_dir, kaisaibi, goods_id):
    """
    画像ファイルのダウンロード
    :param driver: ドライバー
    :param jpg_dir: jpgディレクトリ
    :param resized_jpg_dir: resizedディレクトリ
    :param kaisaibi: 開催日
    :goods_id: 商品ID
    """
    # 商品画像imgを含むli要素を取得
    elements = selenium_util.get_all_elements_by_xpath(
        driver,
        '//*[@id="__next"]/div/div[2]/div[2]/div/div/div[2]/div[1]/div[1]/div[2]/ul/li',
    )

    # 画像ダウンロード
    for i, element in enumerate(elements, start=1):
        # 画像URL
        img_url = ""

        # imgのsrcのurlを取得
        img_url = element.find_element(By.XPATH, ".//img").get_attribute("src")

        # JPG画像ファイル名
        jpg_file = os.path.join(jpg_dir, f"{kaisaibi}_{goods_id}_{i:02}.jpg")
        logger.info(f"JPGファイル：{img_url} -> {jpg_file}")

        # 画像ダウンロード
        selenium_util.download_image(driver, img_url, jpg_file)

        # リサイズJPG画像ファイル名
        resized_jpg_file = os.path.join(
            resized_jpg_dir, f"{kaisaibi}_{goods_id}_{i:02}.jpg"
        )
        logger.info(f"Resized JPGファイル：{resized_jpg_file}")

        # 画像リサイズ
        img_width = 0
        with Image.open(jpg_file) as img:
            img_width = img.width
        img_util.resize_jpg(jpg_file, resized_jpg_file, 347 / img_width)


def make_download_dir(download_dir_name, kaisaibi):
    """
    ダウンロードディレクトリ作成
    :param download_dir_name: ダウンロードディレクトリ名
    :param kaisaibi: 開催日
    :return: ダウンロードディレクトリ
    """
    # ダウンロードディレクトリ
    download_dir = os.path.join(download_dir_name, kaisaibi)

    # ダウンロードディレクトリ作成
    os.makedirs(download_dir, exist_ok=True)

    return download_dir


def make_sub_dir(download_dir, sub_dir_name):
    """
    サブディレクトリ作成
    :param download_dir: ダウンロードディレクトリ
    :param sub_dir_name: サブディレクトリ名
    :return: サブディレクトリ
    """
    # サブディレクトリ
    sub_dir = os.path.join(download_dir, sub_dir_name)

    # サブディレクトリ作成
    os.makedirs(sub_dir, exist_ok=True)

    return sub_dir
