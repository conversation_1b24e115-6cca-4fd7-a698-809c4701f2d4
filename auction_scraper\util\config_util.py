import json
import os
import sys


# 設定ファイルの読み込み
def get_config(config_file):
    """
    設定ファイルを読み込む。
    """
    # カレントディレクトリの取得
    current_dir = get_current_dir()

    # 設定ファイル読み込み
    config_file_path = os.path.join(current_dir, config_file)
    return load_json_file(config_file_path)


# カレントディレクトリの取得
def get_current_dir():
    """
    実行中のスクリプトまたはexeファイルのディレクトリを取得する。
    """
    if getattr(sys, "frozen", False):  # PyInstallerでexe化されている場合
        # exeファイルのある場所を取得
        return os.path.dirname(sys.executable)
    else:
        # スクリプトの実行時に指定されたパスを元に場所を取得
        return os.path.dirname(os.path.abspath(sys.argv[0]))


# JSONファイルの読み込み
def load_json_file(file_path):
    """
    JSON形式ファイルを読み込む。
    """
    with open(file_path, "r", encoding="utf-8") as file:
        return json.load(file)
