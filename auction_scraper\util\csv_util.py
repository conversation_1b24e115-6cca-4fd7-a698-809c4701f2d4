import csv

def save_csv(list, output_file, encoding="utf8"):
    """
    CSVに保存
    """
    with open(output_file, "w", newline="", encoding=encoding) as csvfile:
        fieldnames = list(list[0].keys())
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
        writer.writeheader()
        writer.writerows(list)


def read_csv(file_path, encoding="utf8"):
    """
    CSVファイルを読み込む
    """
    list = []
    with open(file_path, mode="r", encoding=encoding) as csvfile:
        reader = csv.DictReader(csvfile)
        list = [row for row in reader]

    return list