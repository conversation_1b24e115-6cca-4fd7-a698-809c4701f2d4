from logging import getLogger

import openpyxl

logger = getLogger()


def json_to_excel(json_data, output_file):
    """
    JSONデータをExcelに保存
    :param json_data: JSONデータ
    :param output_file: 保存先ファイルパス
    """
    if not json_data:
        logger.info("データが空のため保存しません")
        return

    wb = openpyxl.Workbook()
    ws = wb.active

    # 全てのキーを集めて一意のヘッダーリストを作成（出現順を保持）
    headers = []
    seen = set()
    for item in json_data:
        for key in item.keys():
            if key not in seen:
                headers.append(key)
                seen.add(key)

    # 1行目にタイトル（ヘッダー）を挿入
    for j, header in enumerate(headers):
        ws.cell(row=1, column=j + 1, value=header)

    # 2行目以降にデータを書き込み
    for i, row in enumerate(json_data, start=2):  # データは2行目から
        for j, header in enumerate(headers):
            value = row.get(header, None)  # キーが存在しない場合はNoneを挿入
            ws.cell(row=i, column=j + 1, value=value)

    wb.save(output_file)
