from PIL import Image


def convert_png_to_jpg(png_file_path, jpg_file_path):
    """
    Convert a PNG image to JPG format.

    :param png_file_path: Path to the input PNG file
    :param jpg_file_path: Path to save the output JPG file
    """
    with Image.open(png_file_path) as img:
        rgb_img = img.convert("RGB")
        rgb_img.save(jpg_file_path, "JPEG")


def resize_jpg(jpg_file_path, output_file_path, scale_factor):
    """
    Resize a JPG image by a specified scale factor.

    :param jpg_file_path: Path to the input JPG file
    :param output_file_path: Path to save the resized JPG file
    :param scale_factor: Factor by which to scale the image
    """
    try:
        with Image.open(jpg_file_path) as img:
            new_size = (int(img.width * scale_factor), int(img.height * scale_factor))
            resized_img = img.resize(new_size, Image.Resampling.LANCZOS)
            resized_img.save(output_file_path, "JPEG")
            # 明示的にメモリ解放
            del resized_img
    except Exception as e:
        import logging

        logging.getLogger().error(f"画像リサイズエラー: {jpg_file_path} -> {e}")
        # エラー時は元画像をコピー
        import shutil

        shutil.copy(jpg_file_path, output_file_path)
