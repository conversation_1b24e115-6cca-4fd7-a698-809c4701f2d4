import mimetypes
import smtplib
from email.message import EmailMessage


def send_ms365mail(
    to_email,
    from_email,
    login,
    password,
    smtp_server,
    smtp_port,
    subject,
    content,
    attach_files=None,  # 添付ファイルのリストを追加
):
    """
    Sends an Microsoft365 email using the specified SMTP server and credentials.

    :param to_email: Email address to send the email to.
    :param from_email: Email address to send the email from.
    :param login: Login username for the SMTP server.
    :param password: Login password for the SMTP server.
    :param smtp_server: SMTP server address.
    :param smtp_port: SMTP server port.
    :param subject: Subject of the email.
    :param content: Content of the email.
    :param attach_files: List of file paths to attach to the email.
    :return: None
    """
    # Create the email message
    msg = EmailMessage()
    msg["Subject"] = subject
    msg["From"] = from_email
    msg["To"] = to_email
    msg.set_content(content)

    # Attach files if provided
    if attach_files:
        for file_path in attach_files:
            # Guess the file's MIME type based on its extension
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type is None:
                mime_type = "application/octet-stream"  # Fallback if MIME type cannot be determined
            main_type, sub_type = mime_type.split("/", 1)

            with open(file_path, "rb") as file:
                msg.add_attachment(
                    file.read(),
                    maintype=main_type,
                    subtype=sub_type,
                    filename=file_path.split("/")[-1],
                )

    # Send the email
    with smtplib.SMTP(smtp_server, smtp_port) as server:
        server.ehlo(smtp_server)
        server.starttls()  # Start secure connection
        server.ehlo(smtp_server)
        server.login(login, password)  # Login
        server.send_message(msg)  # Send the email
