import base64
import os
from logging import getLogger

import requests
from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select, WebDriverWait

logger = getLogger()


def get_driver(options):
    """
    Seleniumのドライバーを取得
    """
    # Seleniumのセットアップ
    selenium_options = Options()
    # Chromeのオプションを設定
    if options:
        for option in options:
            selenium_options.add_argument(option)
        logger.info(f"Chromeオプション：{selenium_options.arguments}")

    driver = webdriver.Chrome(options=selenium_options)

    return driver


def get_url(url, params=None, page=0):
    """
    URLを取得
    """
    param_list = None

    if params:  # パラメータがある場合
        param_list = [f"{key}={value}" for key, value in params.items()]

    if page > 1:
        if not param_list:
            param_list = []
        param_list.append(f"page={page}")

    if param_list:
        url += "?" + "&".join(param_list)

    return url


def open_url(driver, url, max_wait_time=10, wait_by=None, wait_name=None):
    """
    指定のURLを開く
    """
    logger.info(f"URLを開く：{url}")
    driver.get(url)
    # 最大待機時間設定（秒）
    wait = WebDriverWait(driver, max_wait_time)
    # 条件が満たされるまで待機（指定の要素がページに表示される）
    if wait_by and wait_name:
        wait.until(EC.presence_of_element_located((wait_by, wait_name)))


def is_element_present_by_css_selector(driver, element_class):
    """
    CSSセレクターで要素が存在するか確認する
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    :return: 要素が存在する場合はTrue、存在しない場合はFalse
    """
    try:
        WebDriverWait(driver, 1).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, element_class))
        )
        return True
    except TimeoutException:
        return False


def get_text_by_css_selector(driver, element_class):
    """
    CSSセレクターで要素を取得し、テキストを取得
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, element_class))
    )
    text = get_text(element)
    return text


def get_text_by_xpath(driver, xpath):
    """
    XPATHで要素を取得し、テキストを取得
    :param driver: ドライバー
    :param xpath: 要素のXPATH
    """
    element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, xpath))
    )
    text = get_text(element)
    return text


def get_text(element):
    """
    要素からテキストを取得
    :param element: 要素
    :return: テキスト
    """
    text = ""
    if element.tag_name == "input":
        text = element.get_attribute("value")
    else:
        text = element.get_attribute("innerHTML").replace("<br>", " ")
    return text


def input_text_by_css_selector(driver, element_class, input_text):
    """
    CSSセレクターでinput要素を取得し、テキストを入力する
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    input_element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, element_class))
    )
    input_element.send_keys(input_text)


def select_option_by_css_selector(driver, element_class, option_value):
    """
    CSSセレクターでselect要素を取得し、オプションを選択する
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    select_element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, element_class))
    )
    select = Select(select_element)
    select.select_by_value(option_value)


def select_option_by_xpath(driver, xpath, option_value):
    """
    XPATHでselect要素を取得し、オプションを選択する
    :param driver: ドライバー
    :param xpath: 要素のXPATH
    """
    select_element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, xpath))
    )
    select = Select(select_element)
    select.select_by_value(option_value)


def click_element_by_css_selector(driver, element_class):
    """
    CSSセレクターで要素を取得し、クリックする
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    # ローディング要素が消えるまで待機
    try:
        WebDriverWait(driver, 30).until(
            EC.invisibility_of_element_located((By.ID, "loader-bg"))
        )
    except TimeoutException:
        pass  # ローディング要素がない場合は続行

    # 要素がクリック可能になるまで待機
    button_element = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, element_class))
    )
    button_element.click()


def click_element_by_xpath(driver, xpath):
    """
    XPATHで要素を取得し、クリックする
    :param driver: ドライバー
    :param xpath: 要素のXPATH
    """
    # ローディング要素が消えるまで待機
    try:
        WebDriverWait(driver, 30).until(
            EC.invisibility_of_element_located((By.ID, "loader-bg"))
        )
    except TimeoutException:
        pass  # ローディング要素がない場合は続行

    # 要素がクリック可能になるまで待機
    button_element = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, xpath))
    )
    button_element.click()


def click_element(driver, element):
    """
    要素を直接指定してクリックする
    :param driver: ドライバー
    :param element: 要素
    """
    # ローディング要素が消えるまで待機
    try:
        WebDriverWait(driver, 30).until(
            EC.invisibility_of_element_located((By.ID, "loader-bg"))
        )
    except TimeoutException:
        pass  # ローディング要素がない場合は続行

    try:
        # JavaScriptのタイムアウト設定を変更
        driver.set_script_timeout(10)  # 10秒に設定

        # 要素が表示されるまで待機
        WebDriverWait(driver, 10).until(EC.visibility_of(element))

        # 要素までスクロール
        driver.execute_script("arguments[0].scrollIntoView(true);", element)

        # 少し待機
        import time

        time.sleep(0.5)

        # JavaScriptでクリック実行（より安定）
        driver.execute_script("arguments[0].click();", element)
    except Exception as e:
        # エラーログ
        import logging

        logger = logging.getLogger()
        logger.warning(f"要素クリックエラー: {e}")

        # 通常のクリックを試みる
        try:
            element.click()
        except Exception as click_error:
            logger.error(f"通常クリックも失敗: {click_error}")
            # 最後の手段としてActionChainsを使用
            from selenium.webdriver.common.action_chains import ActionChains

            ActionChains(driver).move_to_element(element).click().perform()


def is_disabled_element_by_css_selector(driver, element_class):
    """
    CSSセレクターで要素を取得し、disabled属性が設定されている場合はTrueを返す
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    button_element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, element_class))
    )
    is_disabled = button_element.get_attribute("disabled") is not None

    return is_disabled


def download_image(driver, image_url, save_path):
    """
    画像をダウンロードする
    :param driver: ドライバー
    :param image_url: 画像URL
    :param save_path: 保存先ファイルパス
    """
    # クッキーを取得
    cookies = driver.get_cookies()
    # セッションを作成
    session = requests.Session()
    for cookie in cookies:
        session.cookies.set(cookie["name"], cookie["value"])

    # headersにRefererを追加
    headers = {}
    current_url = driver.current_url
    headers.update({"Referer": current_url})

    response = session.get(image_url, headers=headers, stream=True)
    logger.info("response.status_code: " + str(response.status_code))
    if response.status_code == 200:
        with open(save_path, "wb") as file:
            for chunk in response.iter_content(1024):
                file.write(chunk)
    else:
        raise Exception(
            f"画像のダウンロードに失敗しました：{image_url} -> {response.status_code}"
        )


def download_all_images_by_css_selector(
    driver, element_class, download_dir, file_prefix, file_suffix
):
    """
    CSSセレクターで該当するすべての要素を取得し、画像をダウンロードする
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    :param download_dir: ダウンロード先ディレクトリ
    :param file_prefix: ファイル名のプレフィック
    :param file_suffix: ファイル名のサフィックス
    """
    # すべての `img` 要素を取得
    img_elements = driver.find_elements(By.CSS_SELECTOR, element_class)

    # ダウンロード先ディレクトリが存在しない場合は作成
    os.makedirs(download_dir, exist_ok=True)

    for i, img in enumerate(img_elements, start=1):
        img_url = img.get_attribute("src")
        if img_url:
            img_filename = os.path.join(
                download_dir, f"{file_prefix}_{i:02}{file_suffix}"
            )
            logger.info(f"画像：{img_url} -> {img_filename}")
            download_image(driver, img_url, img_filename)


def get_element_by_xpath(driver, xpath):
    """
    XPATHで単要素を取得
    :param driver: ドライバー
    :param xpath: 要素のXPATH
    """
    element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, xpath))
    )

    return element


def get_element_by_css_selector(driver, element_class):
    """
    CSSセレクターで単要素を取得
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, element_class))
    )

    return element


def get_all_elements_by_css_selector(driver, element_class):
    """
    CSSセレクターで複数要素を取得
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    elements = driver.find_elements(By.CSS_SELECTOR, element_class)

    return elements


def get_all_elements_by_xpath(driver, xpath):
    """
    XPATHで複数要素を取得
    :param driver: ドライバー
    :param xpath: 要素のXPATH
    """
    elements = driver.find_elements(By.XPATH, xpath)

    return elements


def download_canvas_image_by_css_selector(driver, canvas_selector, save_path):
    """
    canvas要素から画像を指定ファイル名でダウンロードする
    :param driver: ドライバー
    :param canvas_selector: canvas要素のCSSセレクター
    :param save_path: 保存先ファイルパス
    """
    # 指定のCSSセレクターのcanvas要素が存在しない場合は処理を終了
    try:
        WebDriverWait(driver, 1).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, canvas_selector))
        )
    except TimeoutException:
        return

    # canvasの描画が完了するまで待機
    WebDriverWait(driver, 10).until(lambda d: is_canvas_rendered(d, canvas_selector))

    # JavaScriptを使用してcanvas要素から画像データを取得
    canvas_data = driver.execute_script(
        """
        var canvas = document.querySelector(arguments[0]);
        return canvas.toDataURL('image/png').substring(22);  // "data:image/png;base64,"を除去
    """,
        canvas_selector,
    )

    # 画像データをファイルに保存
    with open(save_path, "wb") as f:
        f.write(base64.b64decode(canvas_data))


def is_canvas_rendered(driver, selector):
    # JavaScriptを使用してcanvasのピクセルデータをチェック
    return driver.execute_script(
        """
        var canvas = document.querySelector(arguments[0]);
        var context = canvas.getContext('2d');
        var pixelData = context.getImageData(0, 0, canvas.width, canvas.height).data;
        for (var i = 0; i < pixelData.length; i += 4) {
            if (pixelData[i] !== 0 || pixelData[i + 1] !== 0 || pixelData[i + 2] !== 0) {
                return true;  // 真っ黒でないピクセルが見つかった
            }
        }
        return false;  // 全てのピクセルが真っ黒
    """,
        selector,
    )


def wait_until_jquery_is_done(driver):
    """
    jQueryの処理がすべて終了するまで待機
    :param driver: ドライバー
    """
    WebDriverWait(driver, 10).until(
        lambda d: d.execute_script("return jQuery.active == 0")
    )


def wait_until_element_present_by_css_selector(driver, element_class):
    """
    指定の要素が表示されるまで待機
    :param driver: ドライバー
    :param element_class: 要素のCSSセレクター
    """
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, element_class))
    )


def wait_until_element_present_by_xpath(driver, xpath):
    """
    指定の要素が表示されるまで待機
    :param driver: ドライバー
    :param xpath: 要素のXPATH
    """
    WebDriverWait(driver, 60).until(EC.presence_of_element_located((By.XPATH, xpath)))
